import 'dart:async';

import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/mixins/after_init.dart';
import 'package:async/async.dart';
import 'package:equatable/equatable.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

enum _EventType {
  text,
  action,
}

class _Event extends Equatable {
  final _EventType type;

  final String value;

  final bool isValid;

  _Event({
    required this.type,
    required this.value,
  }) : isValid = value.isEmpty || double.tryParse(value) != null;

  @override
  List<Object> get props => [type, value];
}

class DropdownFilter<T extends FilterCubit> extends StatefulWidget {  
  final DropdownFilterModel filter;

  const DropdownFilter({
    Key? key,
    required this.filter,
  }) : super(key: key);

  @override
  State<DropdownFilter> createState() => _DropdownFilterState<T>();
}

class _DropdownFilterState<T extends FilterCubit> extends State<DropdownFilter>
    with AfterInitMixin {
  late final TextEditingController _controller;

  late final FocusNode _focusNode;

  late final PublishSubject<_Event> _streamController;

  late Stream<_Event> _eventStream;

  late final StreamSubscription<_Event> _subscription;

  String? _initialValue;

  @override
  void didInitState() {
    final cubit = context.read<T>();

    _streamController = PublishSubject<_Event>();

    List<Stream<_Event>> streams = StreamSplitter.splitFrom(
      _streamController.stream,
      2,
    );

    Stream<_Event> textEventStream = streams[0]
        .distinct()
        .debounceTime(const Duration(milliseconds: 800))
        .where(
          (event) => event.type == _EventType.text,
        );

    Stream<_Event> actionEventStream = streams[1].distinct().where(
          (event) => event.type == _EventType.action,
        );

    _eventStream = StreamGroup.merge(
      [textEventStream, actionEventStream],
    ).asBroadcastStream();

    _subscription = _eventStream.distinct().listen(
      (event) {
        cubit.add(
          id: widget.filter.id,
          value: FilterValueModel<double?>(
            value: double.tryParse(event.value),
          ),
        );
      },
    );

    _controller = TextEditingController();

    _initialValue =
        cubit.state.values[widget.filter.id]!.value?.toString() ?? '';
    _controller.text = _initialValue ?? '';

    _focusNode = FocusNode();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        final String t = _controller.text;

        _streamController.sink.add(
          _Event(
            type: _EventType.text,
            value: t,
          ),
        );

        return;
      }

      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    _streamController.close();
    _controller.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);
    final cubit = context.watch<T>();
    final selected = cubit.state.values[widget.filter.id]! as FilterValueModel<String>;
    final items = widget.filter.values;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FLFilterHeading.regular(
          text: tr(widget.filter.label),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 12),
          child: DropdownButtonFormField<String>(
            value: selected.value,
            isExpanded: true,
            icon: const Icon(Icons.keyboard_arrow_down),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
              ),
            ),
            style: Theme.of(context).textTheme.titleMedium,
            items: items.map<DropdownMenuItem<String>>((item) {
              return DropdownMenuItem<String>(
                value: item.value,
                child: Text(tr(item.label)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                cubit.add(
                  id: widget.filter.id,
                  value: FilterValueModel<String>(value: value),
                );
              }
            },
          ),
        ),
        const FLGap(4),
        const FLDivider(),
      ],
    );
  }
}
